import 'package:flutter/material.dart';
// import 'package:quisipp/screens/home_screen.dart';

// define a class for the onboarding screen
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

// define _OnboardingScreenState class
class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _controller = PageController();

  int _currentIndex = 0;

  final List<Map<String, String>> _pages = [
    {
      "title": "Welcome to Quisipp",
      "description":
          "Your go-to app for discovering and enjoying delicious food from around the world.",
      "image": "assets/images/onboarding/FullTrolley.png",
    },
    {
      "title": "Get any packages delivered",
      "description":
          "We deliver to your doorstep, so you can enjoy your favorite food without leaving your home.",
      "image": "assets/images/onboarding/DeliveryTruck.png",
    },
    {
      "title": "Protected package delivery.",
      "description":
          "Your groceries are carefully packaged to ensure they arrive safely and in perfect condition.",
      "image": "assets/images/onboarding/BigBox.png",
    },
    {
      "title": "Best price guaranteed",
      "description":
          "Allowing you to stock up on your favorite items while staying within your budget.",
      "image": "assets/images/onboarding/CashRegister.png",
    },
  ];

  // function to navigate to the next page
  void _nextPage() {
    if (_currentIndex < _pages.length - 1) {
      _controller.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      Navigator.pushReplacementNamed(
        context,
        '/home'
      );
    }
  }

  // function to skip the onboarding screen
  void _skip() {
    Navigator.pushReplacementNamed(
      context,
      '/home'
    );
  }

  // widget to build the stepper indicator
  Widget _buildStepperIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        _pages.length,
        (index) => AnimatedContainer(
          duration: const Duration(microseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: _currentIndex == index ? 30 : 12,
          height: 12,
          decoration: BoxDecoration(
            color: _currentIndex == index ? Colors.blue : Colors.grey,
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
    );
  }

  // widget to build the onboarding page
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // page view
            Expanded(
              child: PageView.builder(
                controller: _controller,
                itemCount: _pages.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
                itemBuilder: (contex, index) {
                  final item = _pages[index];
                  return Padding(padding: const EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(item["image"]!, height: 200, width: 200),
                        const SizedBox(height: 30),
                        Text(item["title"]!, style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 10),
                        Text(item["description"]!, style: const TextStyle(fontSize: 16), textAlign: TextAlign.center),
                      ],
                    ),
                  );
                },
              ),
            ),
            // stepper indicator
            _buildStepperIndicator(),

            // bottom navigation
            Padding(padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: _skip,
                  child: const Text("Skip", style: TextStyle(fontWeight: FontWeight.bold)),
                ),

                ElevatedButton(
                  onPressed: _nextPage, 
                  child: Text(_currentIndex == _pages.length -1 ? "Get Started" : "Next", style: const TextStyle(fontWeight: FontWeight.bold)),
                )

              ],
            ),
            )
          ],
        ),
      ),
    );
  }
}
